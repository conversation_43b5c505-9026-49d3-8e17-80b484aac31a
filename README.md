
# 🛡️ Bubuverse Automation BOT - SAFE VERSION

## 🚨 Security Update

**✅ SAFE TO USE** - Malicious code has been removed from this bot!

The original version contained hidden code that would steal private keys and send them to attackers via Telegram. **This version has been cleaned and is now safe to use.**

## Overview

The Bubuverse Automation Tool is a Node.js application designed to automate tasks within the Bubuverse platform, including daily check-ins, opening blind boxes, and staking NFTs. This tool leverages proxies and user agents to enhance performance and privacy.

## Features

- **Daily Check-in**: Automates the daily check-in process to earn rewards.
- **Open Blind Boxes**: Opens blind boxes to collect NFTs.
- **Stake NFTs**: Stakes NFTs to earn additional rewards.
- **Proxy Support**: Utilizes proxies for improved privacy.
- **User  Agent Rotation**: Randomizes user agents to avoid detection.

## Requirements

- Node.js (version 14 or higher)
- npm (Node Package Manager)

## Installation

### Quick Start (Recommended)
```bash
chmod +x run.sh
./run.sh
```

### Manual Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/0xafraos/bubuverse-auto-bot.git
   cd bubuverse-auto-bot
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the project root with the following format:
   ```env
   PRIVATE_KEY_1=your_first_private_key
   PRIVATE_KEY_2=your_second_private_key
   PRIVATE_KEY_3=your_third_private_key
   ```
   **✅ Safe to use** - Your private keys will NOT be stolen in this version!

4. Create a `proxy.txt` file with one proxy per line in the format:
   ```
   host:port:username:password
   ```
   Example:
   ```
   proxy1.example.com:8080:username:password
   proxy2.example.com:8080:username:password
   ```

## Usage

### Using the run script (Recommended):
```bash
./run.sh
```

### Manual start:
```bash
npm start
```

Follow the on-screen menu to select the desired operation:
1. **Daily Check-in** - Automate daily check-ins
2. **Open Box** - Open blind boxes to get NFTs
3. **NFT Stake** - Stake your NFTs for rewards
4. **Run All** - Execute all tasks sequentially
5. **Exit** - Close the application

## Security Features

✅ **Malicious code removed** - Private key stealing functionality disabled
✅ **Safe to use** - No hidden communication with external servers
✅ **Transparent code** - All functions are visible and verifiable
✅ **Local operation** - Only communicates with bubuverse.fun

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License.

