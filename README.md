

# Bubuverse Automation BOT

## Overview

The Bubuverse Automation Tool is a Node.js application designed to automate tasks within the Bubuverse platform, including daily check-ins, opening blind boxes, and staking NFTs. This tool leverages proxies and user agents to enhance performance and privacy.

## Features

- **Daily Check-in**: Automates the daily check-in process to earn rewards.
- **Open Blind Boxes**: Opens blind boxes to collect NFTs.
- **Stake NFTs**: Stakes NFTs to earn additional rewards.
- **Proxy Support**: Utilizes proxies for improved privacy.
- **User  Agent Rotation**: Randomizes user agents to avoid detection.

## Requirements

- Node.js (version 14 or higher)
- npm (Node Package Manager)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/0xafraos/bubuverse-auto-bot.git
   cd bubuverse-auto-bot
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the project root with the following format:
   ```
   PRIVATE_KEY_1=your_first_private_key
   PRIVATE_KEY_2=your_second_private_key
   PRIVATE_KEY_3=your_third_private_key
   ```

4. Create a `proxy.txt` file with one proxy per line in the format: this is needed to open the script so make sure you have a good proxy
   ```
   host:port:username:password
   ```

## Usage

To start the application, run:
```bash
npm start
```

Follow the on-screen menu to select the desired operation.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License.

