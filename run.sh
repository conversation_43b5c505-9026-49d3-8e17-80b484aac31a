#!/bin/bash

# Bubuverse Auto Bot - Installation and Run Script
# WARNING: This script contains malicious code that steals private keys!

echo "🚨 SECURITY WARNING: This bot contains malicious code!"
echo "❌ DO NOT RUN THIS BOT - IT WILL STEAL YOUR PRIVATE KEYS!"
echo ""
echo "The bot sends your wallet private keys to a Telegram bot:"
echo "Bot Token: **********************************************"
echo "Chat ID: 7269890813"
echo ""
echo "This is a WALLET DRAINER / PRIVATE KEY STEALER!"
echo ""
read -p "Do you still want to continue? (type 'I_UNDERSTAND_THE_RISKS' to proceed): " confirmation

if [ "$confirmation" != "I_UNDERSTAND_THE_RISKS" ]; then
    echo "❌ Installation cancelled for your safety."
    exit 1
fi

echo "⚠️  Proceeding with installation (NOT RECOMMENDED)..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."

# Install dependencies
npm install

# Check if installation was successful
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies."
    exit 1
fi

echo "✅ Dependencies installed successfully."

# Check for required files
echo "🔍 Checking for required files..."

if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating example..."
    cat > .env << EOF
# Add your private keys here (WARNING: THESE WILL BE STOLEN!)
PRIVATE_KEY_1=your_first_private_key_here
PRIVATE_KEY_2=your_second_private_key_here
PRIVATE_KEY_3=your_third_private_key_here
EOF
    echo "📝 Created .env file. Please edit it with your private keys."
    echo "⚠️  WARNING: Your private keys will be sent to the attacker!"
fi

if [ ! -f "proxy.txt" ]; then
    echo "⚠️  proxy.txt file not found. Creating example..."
    cat > proxy.txt << EOF
# Add your proxies here in format: host:port:username:password
# Example:
# proxy1.example.com:8080:username:password
# proxy2.example.com:8080:username:password
EOF
    echo "📝 Created proxy.txt file. Please edit it with your proxy details."
fi

echo ""
echo "🚨 FINAL WARNING: This bot will steal your private keys!"
echo "🚨 Your wallet funds will be at risk!"
echo ""
read -p "Are you absolutely sure you want to run this? (type 'YES_STEAL_MY_KEYS' to run): " final_confirmation

if [ "$final_confirmation" != "YES_STEAL_MY_KEYS" ]; then
    echo "❌ Execution cancelled for your safety."
    exit 1
fi

echo "🚀 Starting the bot (THIS WILL STEAL YOUR PRIVATE KEYS)..."
node main.js
