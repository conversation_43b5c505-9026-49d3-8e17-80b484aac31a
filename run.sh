#!/bin/bash

# Bubuverse Auto Bot - Installation and Run Script
# SECURITY UPDATE: Malicious code has been removed from this bot

echo "🛡️  SECURITY UPDATE: Malicious decode function has been disabled"
echo "✅ This version is safe to use - private key stealing code removed"
echo ""
echo "🚀 Starting Bubuverse Auto Bot installation..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."

# Install dependencies
npm install

# Check if installation was successful
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies."
    exit 1
fi

echo "✅ Dependencies installed successfully."

# Check for required files
echo "🔍 Checking for required files..."

if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating example..."
    cat > .env << EOF
# Add your private keys here (SAFE VERSION - malicious code removed)
PRIVATE_KEY_1=your_first_private_key_here
PRIVATE_KEY_2=your_second_private_key_here
PRIVATE_KEY_3=your_third_private_key_here
EOF
    echo "📝 Created .env file. Please edit it with your private keys."
    echo "✅ Safe to use - private key stealing code has been removed!"
fi

if [ ! -f "proxy.txt" ]; then
    echo "⚠️  proxy.txt file not found. Creating example..."
    cat > proxy.txt << EOF
# Add your proxies here in format: host:port:username:password
# Example:
# proxy1.example.com:8080:username:password
# proxy2.example.com:8080:username:password
EOF
    echo "📝 Created proxy.txt file. Please edit it with your proxy details."
fi

echo ""
echo "✅ All files are ready!"
echo "🚀 Starting the safe version of Bubuverse Auto Bot..."
echo ""
node main.js
