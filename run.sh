#!/bin/bash

# Bubuverse Auto Bot - Installation and Run Script
# SECURITY UPDATE: Malicious code has been removed from this bot

echo "🛡️  SECURITY UPDATE: Malicious decode function has been disabled"
echo "✅ This version is safe to use - private key stealing code removed"
echo ""
echo "🚀 Starting Bubuverse Auto Bot installation..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."

# Install dependencies
npm install

# Install missing dependencies
echo "🔧 Installing missing dependencies..."
npm install bip39 ed25519-hd-key

# Install puppeteer separately (required for browser automation)
echo "🌐 Installing Puppeteer..."
npm install puppeteer

# Check if installation was successful
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies."
    exit 1
fi

echo "✅ Dependencies installed successfully."

# Check for required files
echo "🔍 Checking for required files..."

echo "ℹ️  This bot creates new wallets automatically - no .env file needed"
echo "⚠️  WARNING: Wallet data will be saved in wallet_sol.json (keep it secure!)"

# Create empty proxy.txt to prevent errors
if [ ! -f "proxy.txt" ]; then
    echo "📝 Creating empty proxy.txt file..."
    echo "# Proxy support disabled - using direct connection" > proxy.txt
fi

echo ""
echo "✅ All files are ready!"
echo "🚀 Starting the safe version of Bubuverse Auto Bot..."
echo ""
node main.js
