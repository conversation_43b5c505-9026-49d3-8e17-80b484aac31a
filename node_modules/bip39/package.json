{"name": "bip39", "version": "3.1.0", "description": "Bitcoin BIP39: Mnemonic code for generating deterministic keys", "main": "src/index.js", "types": "./types/index.d.ts", "scripts": {"build": "npm run clean && tsc -p tsconfig.json", "clean": "rm -rf src", "coverage": "nyc --branches 85 --functions 100 --check-coverage npm run unit", "format": "npm run prettier -- --write", "format:ci": "npm run prettier -- --check", "gitdiff:ci": "npm run build && git diff --exit-code", "lint": "tslint -p tsconfig.json -c tslint.json", "prettier": "prettier 'ts_src/**/*.ts' --ignore-path ./.prettierignore", "test": "npm run build && npm run format:ci && npm run lint && npm run unit", "unit": "tape test/*.js", "update": "node -e \"require('./util/wordlists').update()\""}, "author": "<PERSON>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcousens.com"}], "repository": {"type": "git", "url": "https://github.com/bitcoinjs/bip39.git"}, "license": "ISC", "files": ["src", "types"], "dependencies": {"@noble/hashes": "^1.2.0"}, "devDependencies": {"@types/node": "11.11.6", "node-fetch": "2.6.9", "nyc": "^15.0.0", "prettier": "1.16.4", "proxyquire": "^1.7.10", "tape": "^4.13.2", "tslint": "^6.1.0", "typescript": "3.3.4000"}}