{"name": "create-hmac", "version": "1.1.7", "description": "node style hmacs in the browser", "files": ["browser.js", "index.js", "legacy.js"], "main": "index.js", "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/createHmac.git"}, "keywords": ["crypto", "hmac"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/createHmac/issues"}, "homepage": "https://github.com/crypto-browserify/createHmac", "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^5.3.1", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "browser": "./browser.js"}