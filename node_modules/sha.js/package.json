{"name": "sha.js", "description": "Streamable SHA hashes in pure javascript", "version": "2.4.12", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/sha.js.git"}, "bin": "./bin.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "author": "<PERSON> <<EMAIL>> (dominictarr.com)", "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/crypto-browserify/sha.js", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1", "to-buffer": "^1.2.0"}, "devDependencies": {"@ljharb/eslint-config": "^21.2.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "hash-test-vectors": "^1.3.2", "npmignore": "^0.3.1", "tape": "^5.9.0", "typedarray": "^0.0.7"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", ".github"]}, "engines": {"node": ">= 0.10"}}